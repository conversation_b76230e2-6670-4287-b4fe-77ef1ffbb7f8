package prayer

import (
	"context"
	"time"

	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/hablullah/go-prayer"
)

type sPrayer struct{}

func init() {
	service.RegisterPrayer(New())
}

func New() service.IPrayer {
	return &sPrayer{}
}

// GetPrayerTimes 获取祷告时间
func (s *sPrayer) GetPrayerTimes(ctx context.Context, in *model.PrayerTimeInput) (*model.PrayerTimeOutput, error) {

	date, err := time.Parse("2006-01-02", in.Date)
	if err != nil {
		return nil, gerror.Newf("invalid date format: %s", in.Date)
	}

	config := &model.PrayerCalculationConfig{
		Date:      date,
		Latitude:  in.Latitude,
		Longitude: in.Longitude,
		Timezone:  in.Timezone,
	}

	// 计算祷告时间
	prayerTimes, err := s.calculatePrayerTimes(ctx, config)
	if err != nil {
		return nil, err
	}

	// 转换为伊斯兰历
	islamicDate, err := service.Calendar().ConvertToIslamicDate(ctx, in.Date, "UMMUL_QURA", 0)
	if err != nil {
		g.Log().Warning(ctx, "failed to convert to islamic date:", err)
		islamicDate = &model.IslamicDate{}
	}

	return &model.PrayerTimeOutput{
		PrayerTimes: prayerTimes,
		IslamicDate: islamicDate,
	}, nil
}

// CalculatePrayerTimes 计算祷告时间
func (s *sPrayer) calculatePrayerTimes(ctx context.Context, config *model.PrayerCalculationConfig) (*model.PrayerTimes, error) {
	// 解析时区
	loc, err := time.LoadLocation(config.Timezone)
	if err != nil {
		g.Log().Warning(ctx, "failed to load timezone:", config.Timezone, err)
		loc = time.UTC
	}

	schedules, err := prayer.Calculate(prayer.Config{
		Latitude:           config.Latitude,
		Longitude:          config.Longitude,
		Timezone:           loc,
		TwilightConvention: prayer.MWL(),
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   true,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(2) * time.Minute,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, config.Date.Year())

	// 找到指定日期的祷告时间
	var daySchedule prayer.Schedule
	found := false
	targetDate := config.Date.Format("2006-01-02")
	for _, schedule := range schedules {
		if schedule.Date == targetDate {
			daySchedule = schedule
			found = true
			break
		}
	}

	if !found {
		return nil, gerror.New("prayer times not found for the specified date")
	}

	// 格式化时间
	formatTime := func(t time.Time) string {
		return t.Round(time.Minute).Format("15:04")
	}

	prayerTimes := &model.PrayerTimes{
		Imsak:   formatTime(daySchedule.Fajr.Add(-10 * time.Minute)),
		Subuh:   formatTime(daySchedule.Fajr),
		Terbit:  formatTime(daySchedule.Sunrise),
		Zuhur:   formatTime(daySchedule.Zuhr),
		Ashar:   formatTime(daySchedule.Asr),
		Maghrib: formatTime(daySchedule.Maghrib),
		Isya:    formatTime(daySchedule.Isha),
	}

	// 计算Dhuha时间（日出后15-20分钟）
	dhuhaTime := daySchedule.Sunrise.Add(25 * time.Minute) // TODO: 不是很准，没完全跟nu online对齐
	prayerTimes.Dhuha = formatTime(dhuhaTime)

	return prayerTimes, nil
}
