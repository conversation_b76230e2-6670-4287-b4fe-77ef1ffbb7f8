package model

import (
	"time"
)

// PrayerTimeInput 祷告时间查询输入参数
type PrayerTimeInput struct {
	Date           string  `json:"date" dc:"日期，格式为YYYY-MM-DD"`
	Latitude       float64 `json:"latitude" dc:"纬度"`
	Longitude      float64 `json:"longitude" dc:"经度"`
	Timezone       string  `json:"timezone" dc:"时区"`
	MethodCode     string  `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int32   `json:"date_adjustment" dc:"日期校正"`
}

// PrayerTimeOutput 祷告时间查询输出结果
type PrayerTimeOutput struct {
	PrayerTimes *PrayerTimes `json:"prayer_times" dc:"祷告时间"`
	IslamicDate *IslamicDate `json:"islamic_date" dc:"伊斯兰历日期"`
}

// PrayerTimes 祷告时间
type PrayerTimes struct {
	Imsak   string `json:"imsak" dc:"伊姆萨克时间（仅斋月期间）"`
	Subuh   string `json:"subuh" dc:"晨祷时间"`
	Terbit  string `json:"terbit" dc:"日出时间"`
	Dhuha   string `json:"dhuha" dc:"上午祷告时间（可选）"`
	Zuhur   string `json:"zuhur" dc:"晌祷时间"`
	Ashar   string `json:"ashar" dc:"晡祷时间"`
	Maghrib string `json:"maghrib" dc:"昏祷时间"`
	Isya    string `json:"isya" dc:"宵祷时间"`
}

// IslamicDate 伊斯兰历日期
type IslamicDate struct {
	Year  int32 `json:"year" dc:"伊斯兰历年"`
	Month int32 `json:"month" dc:"伊斯兰历月"`
	Day   int32 `json:"day" dc:"伊斯兰历日"`
}

// PrayerCalculationConfig 祷告时间计算配置
type PrayerCalculationConfig struct {
	Date      time.Time `json:"date" dc:"计算日期"`
	Latitude  float64   `json:"latitude" dc:"用户位置纬度"`
	Longitude float64   `json:"longitude" dc:"用户位置经度"`
	Timezone  string    `json:"timezone" dc:"时区"`
}
