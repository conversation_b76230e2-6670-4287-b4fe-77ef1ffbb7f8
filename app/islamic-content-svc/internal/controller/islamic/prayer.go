package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerPrayer struct {
	v1.UnimplementedPrayerServiceServer
}

// GetPrayerTimes 获取祷告时间
func (*ControllerPrayer) GetPrayerTimes(ctx context.Context, req *v1.GetPrayerTimesReq) (res *v1.GetPrayerTimesRes, err error) {
	input := &model.PrayerTimeInput{
		Date:           time.Now().Format("2006-01-02"), // 使用当前日期
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: req.DateAdjustment,
	}

	output, err := service.Prayer().GetPrayerTimes(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var prayerTimesData *v1.PrayerTimesData
	if err = gconv.Struct(output, &prayerTimesData); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetPrayerTimesRes{
		Code: 200,
		Msg:  "success",
		Data: prayerTimesData,
	}, nil
}
