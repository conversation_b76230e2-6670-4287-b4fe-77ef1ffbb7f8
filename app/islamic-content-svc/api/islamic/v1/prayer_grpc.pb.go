// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/prayer.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PrayerService_GetPrayerTimes_FullMethodName = "/islamic.v1.PrayerService/GetPrayerTimes"
)

// PrayerServiceClient is the client API for PrayerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 祷告时间服务定义
type PrayerServiceClient interface {
	// 获取祷告时间
	GetPrayerTimes(ctx context.Context, in *GetPrayerTimesReq, opts ...grpc.CallOption) (*GetPrayerTimesRes, error)
}

type prayerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrayerServiceClient(cc grpc.ClientConnInterface) PrayerServiceClient {
	return &prayerServiceClient{cc}
}

func (c *prayerServiceClient) GetPrayerTimes(ctx context.Context, in *GetPrayerTimesReq, opts ...grpc.CallOption) (*GetPrayerTimesRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPrayerTimesRes)
	err := c.cc.Invoke(ctx, PrayerService_GetPrayerTimes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrayerServiceServer is the server API for PrayerService service.
// All implementations must embed UnimplementedPrayerServiceServer
// for forward compatibility.
//
// 祷告时间服务定义
type PrayerServiceServer interface {
	// 获取祷告时间
	GetPrayerTimes(context.Context, *GetPrayerTimesReq) (*GetPrayerTimesRes, error)
	mustEmbedUnimplementedPrayerServiceServer()
}

// UnimplementedPrayerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPrayerServiceServer struct{}

func (UnimplementedPrayerServiceServer) GetPrayerTimes(context.Context, *GetPrayerTimesReq) (*GetPrayerTimesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrayerTimes not implemented")
}
func (UnimplementedPrayerServiceServer) mustEmbedUnimplementedPrayerServiceServer() {}
func (UnimplementedPrayerServiceServer) testEmbeddedByValue()                       {}

// UnsafePrayerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrayerServiceServer will
// result in compilation errors.
type UnsafePrayerServiceServer interface {
	mustEmbedUnimplementedPrayerServiceServer()
}

func RegisterPrayerServiceServer(s grpc.ServiceRegistrar, srv PrayerServiceServer) {
	// If the following call pancis, it indicates UnimplementedPrayerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PrayerService_ServiceDesc, srv)
}

func _PrayerService_GetPrayerTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrayerTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetPrayerTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetPrayerTimes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetPrayerTimes(ctx, req.(*GetPrayerTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PrayerService_ServiceDesc is the grpc.ServiceDesc for PrayerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PrayerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.PrayerService",
	HandlerType: (*PrayerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPrayerTimes",
			Handler:    _PrayerService_GetPrayerTimes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/prayer.proto",
}
